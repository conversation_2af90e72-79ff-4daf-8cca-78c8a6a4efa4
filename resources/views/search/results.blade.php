@extends('app')

@section('content')
    @include('includes.header')
    
    <section class="cover">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="spead">
                        <h1>Search Results for "{{ $query }}"</h1>
                        <p>Found {{ count($results) }} results</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    @if(count($results) > 0)
                        <div class="search-results">
                            @foreach($results as $result)
                                <div class="search-result-item">
                                    <div class="row">
                                        @if($result['image'])
                                            <div class="col-md-3">
                                                <div class="result-image">
                                                    <img src="{{ $result['image'] }}" alt="{{ $result['title'] }}" class="img-fluid">
                                                </div>
                                            </div>
                                            <div class="col-md-9">
                                        @else
                                            <div class="col-md-12">
                                        @endif
                                            <div class="result-content">
                                                <div class="result-type">
                                                    <span class="badge badge-primary">{{ $result['type'] }}</span>
                                                </div>
                                                <h3 class="result-title">
                                                    <a href="{{ $result['url'] }}">{{ $result['title'] }}</a>
                                                </h3>
                                                <p class="result-description">
                                                    {{ Str::limit($result['description'], 200) }}
                                                </p>
                                                <a href="{{ $result['url'] }}" class="btn btn-primary">View Details</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                            @endforeach
                        </div>
                    @else
                        <div class="no-results">
                            <div class="text-center">
                                <h3>No results found</h3>
                                <p>Sorry, we couldn't find any results for "{{ $query }}". Try searching with different keywords.</p>
                                <a href="/" class="btn btn-primary">Go to Homepage</a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <style>
        .search-result-item {
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .result-image {
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .result-image img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: 12px;
            transition: transform 0.3s ease;
        }

        .result-image img:hover {
            transform: scale(1.05);
        }

        .result-content {
            padding-left: 20px;
        }

        .result-type {
            margin-bottom: 12px;
        }

        .result-type .badge {
            font-size: 11px;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .result-title {
            margin-bottom: 12px;
        }

        .result-title h3 {
            font-size: 20px;
            font-weight: 600;
            line-height: 1.3;
            margin: 0;
        }

        .result-title a {
            color: #333;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .result-title a:hover {
            color: #007bff;
        }

        .result-description {
            color: #666;
            margin-bottom: 18px;
            line-height: 1.6;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
            color: white;
            text-decoration: none;
        }

        .no-results {
            padding: 60px 0;
            text-align: center;
        }

        .no-results h3 {
            color: #666;
            margin-bottom: 15px;
        }

        .no-results p {
            color: #999;
            margin-bottom: 25px;
        }

        @media (max-width: 768px) {
            .result-content {
                padding-left: 0;
                padding-top: 15px;
            }

            .result-image img {
                height: 150px;
            }
        }
    </style>
@endsection
