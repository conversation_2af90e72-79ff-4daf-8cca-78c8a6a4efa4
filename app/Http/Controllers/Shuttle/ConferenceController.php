<?php

namespace App\Http\Controllers\Shuttle;

use Sina\Shuttle\Models\Component;
use Sina\Shuttle\Models\ScaffoldInterface;
use Sina\Shuttle\Models\PageComponent;
use App\Models\Conference;
use Illuminate\Http\Request;
use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Http\Resources\DataTableResource;

class ConferenceController extends ShuttleController
{
    public function addComponent(Request $request, Conference $conference)
    {
        $lang = 'en';
        $component = Component::find($request->component_id);
        // if (!$component) {
        //     return redirect()->back()->withErrors([
        //         'error' => ['Component not found.']
        //     ]);
        // }
        $conference->components()->attach([
            $component->id => ['setting' => [], 'locale' => $lang, 'model_type' => Conference::class]
        ]);

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function copy(Request $request, Conference $conference)
    {

    }

    public function destroyComponent(Request $request, PageComponent $component)
    {
        $component->delete();

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function addLecturer(Request $request, Conference $conference)
    {
        $request->validate([
            'lecturer_id' => 'required|exists:lecturers,id'
        ]);

        // Check if lecturer is already attached
        if (!$conference->lecturers()->where('lecturer_id', $request->lecturer_id)->exists()) {
            $conference->lecturers()->attach($request->lecturer_id);
        }

        return redirect()->back()->with('success', 'Lecturer added successfully');
    }

    public function removeLecturer(Request $request, Conference $conference, $lecturerId)
    {
        $conference->lecturers()->detach($lecturerId);

        return redirect()->back()->with('success', 'Lecturer removed successfully');
    }

    public function store(ScaffoldInterface $scaffold_interface, Request $request)
    {
        $model = $this->save($scaffold_interface, $request);

        foreach ($request->locations ?? [] as $loc){
            $model->packages()->create($loc);
        }

        return redirect()->route("shuttle.scaffold_interface.index", $scaffold_interface)->with([
            'message' => __('voyager::generic.successfully_updated'),
            'alert-type' => 'success',
        ]);
    }

    public function update(ScaffoldInterface $scaffold_interface, Request $request, $id)
    {
        $model = $this->save($scaffold_interface, $request, $id);

        $ids = [];
        foreach ($request->locations ?? [] as $loc){
            $m = $model->packages()->updateOrCreate(['id' => data_get($loc, 'id')], $loc);
            $ids[] = $m->id;
        }

        $model->packages()->whereNotIn('id', $ids)->delete();

        return redirect()->route("shuttle.scaffold_interface.index", $scaffold_interface)->with([
            'message' => __('voyager::generic.successfully_updated'),
            'alert-type' => 'success',
        ]);
    }

    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface, function ($query) use ($request){
                    if(!empty($request->q)){
                        $query = $query->where('title', 'like', '%'.$request->q.'%');
                    }

                    if(!empty($request->category_id)){
                        $query = $query->where('category_id', $request->category_id);
                    }

                    if(!empty($request->start_at)){
                        $query = $query->whereIn('id', function ($q) use ($request) {
                            $q->select('conference_id')->from('conference_packages')->where('start_at', '>=', $request->start_at);
                        });
                    }

                    if(!empty($request->end_at)){
                        $query = $query->whereIn('id', function ($q) use ($request) {
                            $q->select('conference_id')->from('conference_packages')->where('end_at', '<=', $request->end_at);
                        });
                    }

                    return $query;
                })
//                ->addAction(fn ($data) => '<a href="' . route('shuttle.conference.copy',  $data->id) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.edit', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<button type="button" class="btn btn-bootstrap-padding btn-danger remove-item" data-id="'.$data->id.'"><i class="glyph-icon simple-icon-trash"></i></button>')
        )->json();
    }

}
