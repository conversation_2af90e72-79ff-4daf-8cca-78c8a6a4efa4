<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Conference;
use App\Models\Blog;
use App\Models\Festival;
use App\Models\Awward;

class SearchController extends Controller
{
    public function index(Request $request)
    {
        $query = $request->get('q');
        
        if (empty($query)) {
            return redirect()->back();
        }

        $results = [];

        // Search in Courses
        $courses = Course::where('title', 'LIKE', "%{$query}%")
            ->orWhere('overview', 'LIKE', "%{$query}%")
            ->orWhere('outline', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get();

        foreach ($courses as $course) {
            $results[] = [
                'type' => 'Course',
                'title' => $course->title,
                'description' => strip_tags($course->overview),
                'url' => route('course.show', $course->title),
                'image' => $course->image ? asset('storage/' . $course->image) : null,
            ];
        }

        // Search in Conferences
        $conferences = Conference::where('title', 'LIKE', "%{$query}%")
            ->orWhere('overview', 'LIKE', "%{$query}%")
            ->orWhere('outline', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get();

        foreach ($conferences as $conference) {
            $results[] = [
                'type' => 'Conference',
                'title' => $conference->title,
                'description' => strip_tags($conference->overview),
                'url' => route('conference.show', $conference->title),
                'image' => $conference->cover ? asset('storage/' . $conference->cover) : null,
            ];
        }

        // Search in Blogs
        $blogs = Blog::where('title', 'LIKE', "%{$query}%")
            ->orWhere('content', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get();

        foreach ($blogs as $blog) {
            $results[] = [
                'type' => 'Blog',
                'title' => $blog->title,
                'description' => strip_tags($blog->content),
                'url' => route('blog.show', $blog->slug),
                'image' => $blog->image ? asset('storage/' . $blog->image) : null,
            ];
        }

        // Search in Festivals
        $festivals = Festival::where('title', 'LIKE', "%{$query}%")
            ->orWhere('overview', 'LIKE', "%{$query}%")
            ->orWhere('outline', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get();

        foreach ($festivals as $festival) {
            $results[] = [
                'type' => 'Festival',
                'title' => $festival->title,
                'description' => strip_tags($festival->overview),
                'url' => route('festival.show', $festival->title),
                'image' => $festival->cover ? asset('storage/' . $festival->cover) : null,
            ];
        }

        // Search in Awards
        $awards = Awward::where('title', 'LIKE', "%{$query}%")
            ->orWhere('overview', 'LIKE', "%{$query}%")
            ->orWhere('outline', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get();

        foreach ($awards as $award) {
            $results[] = [
                'type' => 'Award',
                'title' => $award->title,
                'description' => strip_tags($award->overview),
                'url' => route('awward.show', $award->title),
                'image' => $award->cover ? asset('storage/' . $award->cover) : null,
            ];
        }

        return view('search.results', compact('results', 'query'));
    }
}
