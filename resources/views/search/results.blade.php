@extends('app')

@section('content')
    @include('includes.header')
    
    <section class="cover">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="spead">
                        <h1>Search Results for "{{ $query }}"</h1>
                        <p>Found {{ count($results) }} results</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    @if(count($results) > 0)
                        <div class="search-results">
                            @foreach($results as $result)
                                <div class="search-result-item">
                                    <div class="row align-items-center">
                                        @if($result['image'])
                                            <div class="col-md-4 col-lg-3">
                                                <div class="result-image">
                                                    <img src="{{ $result['image'] }}" alt="{{ $result['title'] }}" class="img-fluid">
                                                </div>
                                            </div>
                                            <div class="col-md-8 col-lg-9">
                                        @else
                                            <div class="col-md-12">
                                        @endif
                                            <div class="result-content">
                                                <div class="result-type">
                                                    <span class="badge badge-primary">{{ $result['type'] }}</span>
                                                </div>
                                                <h3 class="result-title">
                                                    <a href="{{ $result['url'] }}">{{ $result['title'] }}</a>
                                                </h3>
                                                <p class="result-description">
                                                    {{ Str::limit($result['description'], 200) }}
                                                </p>
                                                <a href="{{ $result['url'] }}" class="btn btn-primary">
                                                    <i class="fas fa-arrow-right mr-2"></i>View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="no-results">
                            <div class="text-center">
                                <h3>No results found</h3>
                                <p>Sorry, we couldn't find any results for "{{ $query }}". Try searching with different keywords.</p>
                                <a href="/" class="btn btn-primary">Go to Homepage</a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <style>
        .search-result-item {
            padding: 25px 0;
            border-bottom: 1px solid #eee;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .result-image {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .result-image:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .result-image img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: 12px;
            transition: transform 0.3s ease;
        }

        .result-image:hover img {
            transform: scale(1.05);
        }

        .result-content {
            padding-left: 20px;
        }

        .result-type {
            margin-bottom: 12px;
        }

        .result-title {
            margin-bottom: 12px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .result-title a {
            color: #2c3e50;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .result-title a:hover {
            color: #3498db;
        }

        .result-description {
            color: #7f8c8d;
            margin-bottom: 18px;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .no-results {
            padding: 80px 0;
            text-align: center;
        }

        .badge {
            font-size: 11px;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .search-results {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .result-content {
                padding-left: 0;
                margin-top: 20px;
            }

            .result-image img {
                height: 200px;
            }
        }
    </style>
@endsection
