<section class="dashboard">
    <div class="container">
        <div class="row">
            <div class="col-md-12 col-xl-12">
                <div class="dashboard-content">
                    <div class="dashboard-content__title">
                        <h3>History</h3>
                    </div>
                    <div class="dashboard-content__content">
                        @error('success')
                        <div class="alert alert-success">
                            @lang("common.succes_edit")
                        </div>
                        @enderror

                        <div class="dashboard-content__edit">
                            @php
        use Illuminate\Support\Facades\Session;
        use Illuminate\Support\Facades\DB;

        // Example using session (you can also use auth()->id())
        $userId = auth()->id(); // Or: auth()->id();
        // Fetch transactions from database
        $transactions = DB::table('transactions')->where('user_id', $userId)->get();
    @endphp

    <table class="table table-bordered table-striped">
        <thead class="table-dark">
            <tr>
                <th>#</th>
                <th>Price</th>
                <th>Payemnt Method</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
            @forelse($transactions as $index => $transaction)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>£{{ number_format($transaction->price, 2) }}</td>
                    <td>{{$transaction->payment_method }}</td>
                    <td>{{ \Carbon\Carbon::parse($transaction->created_at)->format('d M Y') }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="3" class="text-center">No transactions found</td>
                </tr>
            @endforelse
        </tbody>
    </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>