<section class="dashboard">
    <div class="container">
        <div class="row">
            <div class="col-md-12 col-xl-12">
                <div class="dashboard-content">
                    {{--                    <div class="dashboard-content__title"> --}}
                    {{--                        <h3>@lang("common.my_purchases")</h3> --}}
                    {{--                        <a href="{{ route('course.index') }}">@lang("common.all_course")</a> --}}
                    {{--                    </div> --}}
                    <div class="dashboard-content__content">

                        @if ($myCourse->count())
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="section-title">
                                        <h1>@lang('common.my_courses')</h1>
                                    </div>
                                </div>

                                @foreach ($myCourse as $c)
                                    @if ($c->model)


                                    <div class="col-md-6">
                                        <div class="l">
                                            <div class="l-item">
                                                <div class="l-item__title">
                                                    <span>{{ $c->model->course->category?->name ?? 'N/A' }}</span>
                                                    <h2><a
                                                            href="https://business-eagles.shuttle.ge/courses/1">{{ $c->model->course->title }}</a>
                                                    </h2>
                                                    <ul>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px" viewBox="0 0 24 24"
                                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                    <path
                                                                        d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M15 3C16.95 8.84 16.95 15.16 15 21"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                    <path
                                                                        d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>@lang('common.location')</p>
                                                                <span>{{ $c->model->location?->name ?? 'N/A' }}</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg fill="#000000" width="800px" height="800px"
                                                                    viewBox="0 0 24 24" id="Layer_1"
                                                                    data-name="Layer 1"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M24,12a1,1,0,0,1-2,0A10.011,10.011,0,0,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12Zm-8,1a1,1,0,0,0,0-2H13.723A2,2,0,0,0,13,10.277V7a1,1,0,0,0-2,0v3.277A1.994,1.994,0,1,0,13.723,13ZM1.827,6.784a1,1,0,1,0,1,1A1,1,0,0,0,1.827,6.784ZM2,12a1,1,0,1,0-1,1A1,1,0,0,0,2,12ZM12,22a1,1,0,1,0,1,1A1,1,0,0,0,12,22ZM4.221,3.207a1,1,0,1,0,1,1A1,1,0,0,0,4.221,3.207ZM7.779.841a1,1,0,1,0,1,1A1,1,0,0,0,7.779.841ZM1.827,15.216a1,1,0,1,0,1,1A1,1,0,0,0,1.827,15.216Zm2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,4.221,18.793Zm3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,7.779,21.159Zm14.394-5.943a1,1,0,1,0,1,1A1,1,0,0,0,22.173,15.216Zm-2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,19.779,18.793Zm-3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,16.221,21.159Z">
                                                                    </path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>@lang('common.duration')</p>
                                                                <span>{{ $c->model->duration }} @lang('common.day')</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px" viewBox="0 0 24 24"
                                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <g>
                                                                        <path id="Vector"
                                                                            d="M4 8H20M4 8V16.8002C4 17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20 7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20 18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547 4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822 5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M16 2V4M16 4H8M8 2V4"
                                                                            stroke="#000000" stroke-width="2"
                                                                            stroke-linecap="round"
                                                                            stroke-linejoin="round"></path>
                                                                    </g>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>@lang('common.upcoming')</p>
                                                                <span>{{ $c->model->upComingFormat }}</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <a href="{{ route('course.show', ['course_title' => Str::slug($c->model->course->title, '-')]) }}"
                                                        class="btn-reg"><svg width="800px" height="800px"
                                                            viewBox="0 0 24 24" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7"
                                                                stroke="#000000" stroke-width="1.5"
                                                                stroke-linecap="round" stroke-linejoin="round"></path>
                                                        </svg> @lang('common.see_more')</a>
                                                </div>
                                                <div class="l-item__img">
                                                    <figure><img src="{{ Storage::url($c->model->course->cover) }}"
                                                            alt="" /></figure>
                                                    <div class="price">£{{ $c->model->price }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                @endforeach
                            </div>
                        @endif
                        @if ($myConference->count())
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="section-title">
                                        <h1>@lang('common.my_conferences')</h1>
                                    </div>
                                </div>


                                @foreach ($myConference as $item)
                                    <div class="col-md-12">
                                        <div class="l l-c">
                                            <div class="l-item">
                                                <div class="l-item__title">
                                                    <span>{{ $item->model->conference->category?->name }}</span>
                                                    <h2><a
                                                            href="{{ route('conference.show', Str::slug($item->model->conference->title, '-')) }}">{{ $item->model->conference->title }}</a>
                                                    </h2>
                                                    <ul>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px" viewBox="0 0 24 24"
                                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                    <path
                                                                        d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M15 3C16.95 8.84 16.95 15.16 15 21"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round" stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"></path>
                                                                    <path
                                                                        d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"></path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.location</p>
                                                                <span>{{ $item->model->conference->location?->name ?? 'N/A' }}</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg fill="#000000" width="800px" height="800px"
                                                                    viewBox="0 0 24 24" id="Layer_1"
                                                                    data-name="Layer 1"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M24,12a1,1,0,0,1-2,0A10.011,10.011,0,0,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12Zm-8,1a1,1,0,0,0,0-2H13.723A2,2,0,0,0,13,10.277V7a1,1,0,0,0-2,0v3.277A1.994,1.994,0,1,0,13.723,13ZM1.827,6.784a1,1,0,1,0,1,1A1,1,0,0,0,1.827,6.784ZM2,12a1,1,0,1,0-1,1A1,1,0,0,0,2,12ZM12,22a1,1,0,1,0,1,1A1,1,0,0,0,12,22ZM4.221,3.207a1,1,0,1,0,1,1A1,1,0,0,0,4.221,3.207ZM7.779.841a1,1,0,1,0,1,1A1,1,0,0,0,7.779.841ZM1.827,15.216a1,1,0,1,0,1,1A1,1,0,0,0,1.827,15.216Zm2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,4.221,18.793Zm3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,7.779,21.159Zm14.394-5.943a1,1,0,1,0,1,1A1,1,0,0,0,22.173,15.216Zm-2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,19.779,18.793Zm-3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,16.221,21.159Z">
                                                                    </path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.duration</p>
                                                                <span>{{ $item->model->duration }} common.day</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px"
                                                                    viewBox="0 0 24 24" fill="none"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <g id="Calendar / Calendar">
                                                                        <path id="Vector"
                                                                            d="M4 8H20M4 8V16.8002C4 17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20 7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20 18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547 4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822 5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M16 2V4M16 4H8M8 2V4"
                                                                            stroke="#000000" stroke-width="2"
                                                                            stroke-linecap="round"
                                                                            stroke-linejoin="round"></path>
                                                                    </g>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.upcoming</p>
                                                                <span>{{ $item->model->upComingFormat }}</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <a href="{{ route('conference.show', Str::slug($item->model->conference->title, '-')) }}"
                                                        class="btn-reg"><svg width="800px" height="800px"
                                                            viewBox="0 0 24 24" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7"
                                                                stroke="#000000" stroke-width="1.5"
                                                                stroke-linecap="round" stroke-linejoin="round"></path>
                                                        </svg> @lang('common.see_more') </a>
                                                </div>
                                                <div class="l-item__img">
                                                    <figure><img
                                                            src="{{ Storage::url($item->model->conference->cover) }}"
                                                            alt=""></figure>
                                                    <div class="price">£{{ $item->model->price }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        @if ($myAwward->count())
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="section-title">
                                        <h1>@lang('common.my_awwards')</h1>
                                    </div>
                                </div>
                                @foreach ($myAwward as $item)
                                    <div class="col-md-12">
                                        <div class="l l-c">
                                            <div class="l-item">
                                                <div class="l-item__title">
                                                    <span>{{ $item->model->awward->category?->name }}</span>
                                                    <h2><a
                                                            href="{{ route('awward.show', Str::slug($item->model->awward->title, '-')) }}">{{ $item->model->awward->title }}</a>
                                                    </h2>
                                                    <ul>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px"
                                                                    viewBox="0 0 24 24" fill="none"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round">
                                                                    </path>
                                                                    <path
                                                                        d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M15 3C16.95 8.84 16.95 15.16 15 21"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"></path>
                                                                    <path
                                                                        d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"></path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.location</p>
                                                                <span>{{ $item->model->awward->location?->name ?? 'N/A' }}</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg fill="#000000" width="800px" height="800px"
                                                                    viewBox="0 0 24 24" id="Layer_1"
                                                                    data-name="Layer 1"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M24,12a1,1,0,0,1-2,0A10.011,10.011,0,0,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12Zm-8,1a1,1,0,0,0,0-2H13.723A2,2,0,0,0,13,10.277V7a1,1,0,0,0-2,0v3.277A1.994,1.994,0,1,0,13.723,13ZM1.827,6.784a1,1,0,1,0,1,1A1,1,0,0,0,1.827,6.784ZM2,12a1,1,0,1,0-1,1A1,1,0,0,0,2,12ZM12,22a1,1,0,1,0,1,1A1,1,0,0,0,12,22ZM4.221,3.207a1,1,0,1,0,1,1A1,1,0,0,0,4.221,3.207ZM7.779.841a1,1,0,1,0,1,1A1,1,0,0,0,7.779.841ZM1.827,15.216a1,1,0,1,0,1,1A1,1,0,0,0,1.827,15.216Zm2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,4.221,18.793Zm3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,7.779,21.159Zm14.394-5.943a1,1,0,1,0,1,1A1,1,0,0,0,22.173,15.216Zm-2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,19.779,18.793Zm-3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,16.221,21.159Z">
                                                                    </path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.duration</p>
                                                                <span>{{ $item->model->duration }} common.day</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px"
                                                                    viewBox="0 0 24 24" fill="none"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <g id="Calendar / Calendar">
                                                                        <path id="Vector"
                                                                            d="M4 8H20M4 8V16.8002C4 17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20 7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20 18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547 4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822 5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M16 2V4M16 4H8M8 2V4"
                                                                            stroke="#000000" stroke-width="2"
                                                                            stroke-linecap="round"
                                                                            stroke-linejoin="round"></path>
                                                                    </g>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.upcoming</p>
                                                                <span>{{ $item->model->upComingFormat }}</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <a href="{{ route('awward.show', Str::slug($item->model->awward->title, '-')) }}"
                                                        class="btn-reg"><svg width="800px" height="800px"
                                                            viewBox="0 0 24 24" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7"
                                                                stroke="#000000" stroke-width="1.5"
                                                                stroke-linecap="round" stroke-linejoin="round"></path>
                                                        </svg> @lang('common.see_more') </a>
                                                </div>
                                                <div class="l-item__img">
                                                    <figure><img src="{{ Storage::url($item->model->awward->cover) }}"
                                                            alt=""></figure>
                                                    <div class="price">£{{ $item->model->price }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        @if ($myFestival->count())
                            <div class="row">

                                <div class="col-md-12">
                                    <div class="section-title">
                                        <h1>@lang('common.my_festivals')</h1>
                                    </div>
                                </div>
                                @foreach ($myFestival as $item)
                                    <div class="col-md-12">
                                        <div class="l l-c">
                                            <div class="l-item">
                                                <div class="l-item__title">
                                                    <span>{{ $item->model->festival->category?->name }}</span>
                                                    <h2><a
                                                            href="{{ route('festival.show', Str::slug($item->model->festival->title, '-')) }}">{{ $item->model->festival->title }}</a>
                                                    </h2>
                                                    <ul>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px"
                                                                    viewBox="0 0 24 24" fill="none"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round">
                                                                    </path>
                                                                    <path
                                                                        d="M7.99998 3H8.99998C7.04998 8.84 7.04998 15.16 8.99998 21H7.99998"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M15 3C16.95 8.84 16.95 15.16 15 21"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round">
                                                                    </path>
                                                                    <path d="M3 16V15C8.84 16.95 15.16 16.95 21 15V16"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"></path>
                                                                    <path
                                                                        d="M3 9.0001C8.84 7.0501 15.16 7.0501 21 9.0001"
                                                                        stroke="#292D32" stroke-width="1.5"
                                                                        stroke-linecap="round"
                                                                        stroke-linejoin="round"></path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.location</p>
                                                                <span>{{ $item->model->festival->location?->name ?? 'N/A' }}</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg fill="#000000" width="800px" height="800px"
                                                                    viewBox="0 0 24 24" id="Layer_1"
                                                                    data-name="Layer 1"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M24,12a1,1,0,0,1-2,0A10.011,10.011,0,0,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12Zm-8,1a1,1,0,0,0,0-2H13.723A2,2,0,0,0,13,10.277V7a1,1,0,0,0-2,0v3.277A1.994,1.994,0,1,0,13.723,13ZM1.827,6.784a1,1,0,1,0,1,1A1,1,0,0,0,1.827,6.784ZM2,12a1,1,0,1,0-1,1A1,1,0,0,0,2,12ZM12,22a1,1,0,1,0,1,1A1,1,0,0,0,12,22ZM4.221,3.207a1,1,0,1,0,1,1A1,1,0,0,0,4.221,3.207ZM7.779.841a1,1,0,1,0,1,1A1,1,0,0,0,7.779.841ZM1.827,15.216a1,1,0,1,0,1,1A1,1,0,0,0,1.827,15.216Zm2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,4.221,18.793Zm3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,7.779,21.159Zm14.394-5.943a1,1,0,1,0,1,1A1,1,0,0,0,22.173,15.216Zm-2.394,3.577a1,1,0,1,0,1,1A1,1,0,0,0,19.779,18.793Zm-3.558,2.366a1,1,0,1,0,1,1A1,1,0,0,0,16.221,21.159Z">
                                                                    </path>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.duration</p>
                                                                <span>{{ $item->model->duration }} common.day</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <figure>
                                                                <svg width="800px" height="800px"
                                                                    viewBox="0 0 24 24" fill="none"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    <g id="Calendar / Calendar">
                                                                        <path id="Vector"
                                                                            d="M4 8H20M4 8V16.8002C4 17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20 7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20 18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547 4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822 5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M16 2V4M16 4H8M8 2V4"
                                                                            stroke="#000000" stroke-width="2"
                                                                            stroke-linecap="round"
                                                                            stroke-linejoin="round"></path>
                                                                    </g>
                                                                </svg>
                                                            </figure>
                                                            <div class="title-init">
                                                                <p>common.upcoming</p>
                                                                <span>{{ $item->model->upComingFormat }}</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <a href="{{ route('festival.show', Str::slug($item->model->festival->title, '-')) }}"
                                                        class="btn-reg"><svg width="800px" height="800px"
                                                            viewBox="0 0 24 24" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M15 11C15 12.6569 13.6569 14 12 14C10.3431 14 9 12.6569 9 11M4 7H20M4 7V13C4 19.3668 5.12797 20.5 12 20.5C18.872 20.5 20 19.3668 20 13V7M4 7L5.44721 4.10557C5.786 3.428 6.47852 3 7.23607 3H16.7639C17.5215 3 18.214 3.428 18.5528 4.10557L20 7"
                                                                stroke="#000000" stroke-width="1.5"
                                                                stroke-linecap="round" stroke-linejoin="round"></path>
                                                        </svg> @lang('common.see_more') </a>
                                                </div>
                                                <div class="l-item__img">
                                                    <figure><img
                                                            src="{{ Storage::url($item->model->festival->cover) }}"
                                                            alt=""></figure>
                                                    <div class="price">£{{ $item->model->price }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif

                        @if (!$myConference && !$myCourse->count() && !$myFestival->count() && !$myAwward->count())
                            <div class="not-found">
                                <svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="790"
                                    height="512.20805" viewBox="0 0 790 512.20805"
                                    xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <path
                                        d="M925.56335,704.58909,903,636.49819s24.81818,24.81818,24.81818,45.18181l-4.45454-47.09091s12.72727,17.18182,11.45454,43.27273S925.56335,704.58909,925.56335,704.58909Z"
                                        transform="translate(-205 -193.89598)" fill="#e6e6e6" />
                                    <path
                                        d="M441.02093,642.58909,419,576.13509s24.22155,24.22155,24.22155,44.09565l-4.34745-45.95885s12.42131,16.76877,11.17917,42.23245S441.02093,642.58909,441.02093,642.58909Z"
                                        transform="translate(-205 -193.89598)" fill="#e6e6e6" />
                                    <path
                                        d="M784.72555,673.25478c.03773,43.71478-86.66489,30.26818-192.8092,30.35979s-191.53562,13.68671-191.57335-30.028,86.63317-53.29714,192.77748-53.38876S784.68782,629.54,784.72555,673.25478Z"
                                        transform="translate(-205 -193.89598)" fill="#e6e6e6" />
                                    <rect y="509.69312" width="790" height="2" fill="#3f3d56" />
                                    <polygon
                                        points="505.336 420.322 491.459 420.322 484.855 366.797 505.336 366.797 505.336 420.322"
                                        fill="#a0616a" />
                                    <path
                                        d="M480.00587,416.35743H508.3101a0,0,0,0,1,0,0V433.208a0,0,0,0,1,0,0H464.69674a0,0,0,0,1,0,0v-1.54149A15.30912,15.30912,0,0,1,480.00587,416.35743Z"
                                        fill="#2f2e41" />
                                    <polygon
                                        points="607.336 499.322 593.459 499.322 586.855 445.797 607.336 445.797 607.336 499.322"
                                        fill="#a0616a" />
                                    <path
                                        d="M582.00587,495.35743H610.3101a0,0,0,0,1,0,0V512.208a0,0,0,0,1,0,0H566.69674a0,0,0,0,1,0,0v-1.54149A15.30912,15.30912,0,0,1,582.00587,495.35743Z"
                                        fill="#2f2e41" />
                                    <path
                                        d="M876.34486,534.205A10.31591,10.31591,0,0,0,873.449,518.654l-32.23009-131.2928L820.6113,396.2276l38.33533,126.949a10.37185,10.37185,0,0,0,17.39823,11.0284Z"
                                        transform="translate(-205 -193.89598)" fill="#a0616a" />
                                    <path
                                        d="M851.20767,268.85955a11.38227,11.38227,0,0,0-17.41522,1.15247l-49.88538,5.72709,7.58861,19.24141,45.36779-8.49083a11.44393,11.44393,0,0,0,14.3442-17.63014Z"
                                        transform="translate(-205 -193.89598)" fill="#a0616a" />
                                    <path
                                        d="M769,520.58909l21.76811,163.37417,27.09338-5.578s-3.98437-118.98157,9.56238-133.32513S810,505.58909,810,505.58909Z"
                                        transform="translate(-205 -193.89598)" fill="#2f2e41" />
                                    <path
                                        d="M778,475.58909l-10,15s-77-31.99929-77,19-4.40631,85.60944-6,88,18.43762,8.59375,28,7c0,0,11.79687-82.21884,11-87,0,0,75.53355,37.03335,89.87712,33.84591S831.60944,536.964,834,530.58909s-1-57-1-57l-47.81-14.59036Z"
                                        transform="translate(-205 -193.89598)" fill="#2f2e41" />
                                    <path
                                        d="M779.34915,385.52862l-2.85032-3.42039s-31.92361-71.82815-19.3822-91.21035,67.26762-22.23252,68.97783-21.0924-4.08488,15.9428-.09446,22.78361c0,0-42.394,9.19121-45.24435,10.33134s21.96615,43.2737,21.96615,43.2737l-2.85031,25.6529Z"
                                        transform="translate(-205 -193.89598)" fill="#ccc" />
                                    <path
                                        d="M835.21549,350.18459S805.57217,353.605,804.432,353.605s-1.71017-7.41084-1.71017-7.41084l-26.223,35.91406S763.57961,486.29929,767,484.58909s66.50531,8.11165,67.07539,3.55114-.57008-27.3631,1.14014-28.50324,29.64328-71.82811,29.64328-71.82811-2.85032-14.82168-12.54142-19.95227S835.21549,350.18459,835.21549,350.18459Z"
                                        transform="translate(-205 -193.89598)" fill="#ccc" />
                                    <path
                                        d="M855.73783,378.11779l9.121,9.69109S878.41081,499.1687,871,502.58909s-22,3-22,3l-14.35458-52.79286Z"
                                        transform="translate(-205 -193.89598)" fill="#ccc" />
                                    <circle cx="601.72966" cy="122.9976" r="26.2388" fill="#a0616a" />
                                    <path
                                        d="M800.57267,320.98789c-.35442-5.44445-7.22306-5.631-12.67878-5.68255s-11.97836.14321-15.0654-4.35543c-2.0401-2.973-1.65042-7.10032.035-10.28779s4.45772-5.639,7.18508-7.99742c7.04139-6.08884,14.29842-12.12936,22.7522-16.02662s18.36045-5.472,27.12788-2.3435c10.77008,3.84307,25.32927,23.62588,26.5865,34.99176s-3.28507,22.95252-10.9419,31.44586-25.18188,5.0665-36.21069,8.088c6.7049-9.48964,2.28541-26.73258-8.45572-31.164Z"
                                        transform="translate(-205 -193.89598)" fill="#2f2e41" />
                                    <circle cx="361.7217" cy="403.5046" r="62.98931" fill="#d02333" />
                                    <path
                                        d="M524.65625,529.9355a45.15919,45.15919,0,0,1-41.25537-26.78614L383.44873,278.05757a59.83039,59.83039,0,1,1,111.87012-41.86426l72.37744,235.41211a45.07978,45.07978,0,0,1-43.04,58.33008Z"
                                        transform="translate(-205 -193.89598)" fill="#d02333" />
                                </svg>
                                <h3>@lang('common.not_found')</h3>
                                <a href="/contact-us">@lang('common.contact_us')</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
