@extends('app')

@section('content')
    @include('includes.header')
    
    <section class="cover">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="spead">
                        <h1>Search Results for "{{ $query }}"</h1>
                        <p>Found {{ count($results) }} results</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    @if(count($results) > 0)
                        <div class="search-results">
                            @foreach($results as $result)
                                <div class="search-result-item">
                                    <div class="row">
                                        @if($result['image'])
                                            <div class="col-md-3">
                                                <div class="result-image">
                                                    <img src="{{ $result['image'] }}" alt="{{ $result['title'] }}" class="img-fluid">
                                                </div>
                                            </div>
                                            <div class="col-md-9">
                                        @else
                                            <div class="col-md-12">
                                        @endif
                                            <div class="result-content">
                                                <div class="result-type">
                                                    <span class="badge badge-primary">{{ $result['type'] }}</span>
                                                </div>
                                                <h3 class="result-title">
                                                    <a href="{{ $result['url'] }}">{{ $result['title'] }}</a>
                                                </h3>
                                                <p class="result-description">
                                                    {{ Str::limit($result['description'], 200) }}
                                                </p>
                                                <a href="{{ $result['url'] }}" class="btn btn-primary">View Details</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                            @endforeach
                        </div>
                    @else
                        <div class="no-results">
                            <div class="text-center">
                                <h3>No results found</h3>
                                <p>Sorry, we couldn't find any results for "{{ $query }}". Try searching with different keywords.</p>
                                <a href="/" class="btn btn-primary">Go to Homepage</a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <style>
        .search-result-item {
            padding: 20px 0;
        }
        
        .result-image {
            overflow: hidden;
            border-radius: 8px;
        }

        .result-image img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .result-type {
            margin-bottom: 10px;
        }
        
        .result-title {
            margin-bottom: 10px;
        }
        
        .result-title a {
            color: #333;
            text-decoration: none;
        }
        
        .result-title a:hover {
            color: #007bff;
        }
        
        .result-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        .no-results {
            padding: 60px 0;
        }
        
        .badge {
            font-size: 12px;
            padding: 4px 8px;
        }

        /* Responsive ზომები */
        @media (max-width: 768px) {
            .result-image img {
                height: 150px;
            }
        }

        @media (max-width: 576px) {
            .result-image img {
                height: 120px;
            }

            .search-result-item {
                padding: 15px 0;
            }
        }
    </style>
@endsection
